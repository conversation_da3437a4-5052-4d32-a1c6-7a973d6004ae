<script setup lang="ts">
  import type { Dayjs } from 'dayjs';

  import { computed, onMounted, onUnmounted, reactive, ref, watch } from 'vue';
  import { useRouter } from 'vue-router';

  import { useUserStore } from '@vben/stores';

  import { message } from 'ant-design-vue';
  import dayjs from 'dayjs';

  import { getVoucherList } from '#/api/account-book/bookkeeping/index';
  // AI凭证相关API
  import {
    deleteVouchers,
    getCurrentVouchers,
    mergeVouchers,
    writeBackVouchers,
  } from '#/api/original-voucher/api-v2';
  // AI功能相关导入
  import { useCompanySelection } from '#/components/ai-chat/composables/useCompanySelection';
  import { useSubjectData } from '#/hooks/account-book/voucher/index';
  import { useGlobalLoading } from '#/hooks/useGlobalLoading';
  import { useMonthSelectionStore } from '#/store/modules/month-selection';
  import { useVoucherStore } from '#/store/modules/voucher';
  import { uTgetTime } from '#/utils/index';
  import OuterBoundary from '#/views/jsj-ai/components/outer-boundary.vue';

  // AI功能相关store
  const voucherStore = useVoucherStore();
  const monthSelectionStore = useMonthSelectionStore();
  const userStore = useUserStore();
  const { companyList, fetchCompanyNames, selectedCompany, selectedMonth } =
    useCompanySelection();

  // 路由实例
  const router = useRouter();

  // 开发环境标识
  const isDev = import.meta.env.DEV;

  const dateFormat = 'YYYY-MM';
  type RangeValue = [Dayjs, Dayjs];
  const currdate = uTgetTime();
  const timeInterval = ref<RangeValue>([
    dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
    dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
  ]);
  const seachInput = ref<string>('');
  const moreSeachShow = ref<boolean>(false);
  const sortType = ref<string>('0');
  const voucherList = ref<any[]>([]);
  // const changeVoucherNo = ref<string>(''); // 修改编号输入框值 - 暂时未使用
  const useLoading = useGlobalLoading();
  const currPage = ref<number>(1);
  const pageSize = ref<number>(20);
  const selectAll = ref<boolean>(false);
  const allTotalLength = ref<number>(0);
  let currentModificationMumberIndex = -1;

  // AI功能相关响应式数据
  const hasGeneratedVoucher = ref(false);

  // 合并功能相关响应式数据
  const mergeConfirmVisible = ref(false);
  const mergeLoading = ref(false);

  // 删除功能相关响应式数据
  const deleteConfirmVisible = ref(false);
  const deleteLoading = ref(false);
  // 更多搜索
  const searchesState = reactive({
    abstract: '', // 摘要
    accountingInput: '', // 辅助核算输入框
    accountingType: 'All', // 辅助核算
    accountNumber: '', // 科目编号
    book_type_val: '不限',
    checked1: false,
    checked2: false,
    endTime: dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
    moneyMax: '', // 最大
    moneyMin: '', // 最小
    source: 'All', // 来源
    starTime: dayjs(`${currdate[0]}-${currdate[1]}`, dateFormat),
    voucherNumber: '', // 更多搜索里的凭证号码
  });
  const useSubject = useSubjectData();

  const getList = async (page: number, _isMore?: boolean) => {
    const todata: any = {
      auxiliaryType: searchesState.accountingType, // 辅助核算
      endDate: timeInterval.value[1].format('YYYY-MM'),
      pageSize: pageSize.value,
      startDate: timeInterval.value[0].format('YYYY-MM'),
      voucherType: searchesState.source,
      voucherWord: searchesState.book_type_val,
    };
    if (searchesState.voucherNumber || seachInput.value) {
      todata.voucherNo = searchesState.voucherNumber || seachInput.value;
    }
    if (searchesState.accountNumber) {
      todata.subjectCode = searchesState.accountNumber;
    }
    if (searchesState.accountingInput || seachInput.value) {
      todata.auxiliaryCode = searchesState.accountingInput || seachInput.value;
    }
    if (searchesState.moneyMin) {
      todata.startPrice = searchesState.moneyMin;
    }
    if (searchesState.moneyMax) {
      todata.endPrice = searchesState.moneyMax;
    }
    if (searchesState.abstract) {
      todata.summary = searchesState.abstract;
    }
    if (searchesState.checked1) {
      todata.isCashflow = true;
    }
    if (searchesState.checked2) {
      todata.isAuditComment = true;
    }
    useLoading.setShow(true);
    /**
     * {
            "voucherWord": "记",
            "auxiliaryType": "Supplier",
            "voucherType": "NORMAL",
            "startDate": "2025-05",
            "endDate": "2025-05",
            "voucherNo": "1212",
            "subjectCode": "1001",
            "auxiliaryCode": "天津",
            "startPrice": "1",
            "endPrice": "180",
            "summary": "这是摘要",
            "isCashflow": true,
            "isAuditComment": true
        }
     */
    getVoucherList(todata, page, sortType.value)
      .then((res: any) => {
        if (res.returnCode === '200') {
          currPage.value = page;
          // 数据需要处理下添加两个参数
          // 是否选中参数 修改凭证字号的输入框是否显示
          res.data.forEach((v: any) => {
            v.isChecked = false; // 是否选中
            v.inputIsShow = false;
          });
          // 总条数
          allTotalLength.value = res.data.total;
          voucherList.value = res.data;
        } else {
          message.warning(res.returnMsg);
        }
        useLoading.setShow(false);
      })
      .catch(() => {
        useLoading.setShow(false);
      });
  };
  onMounted(async () => {
    if (useSubject.selectdata.value.length === 0) {
      useSubject.fetchData();
    }

    // 获取公司列表
    try {
      await fetchCompanyNames();

      // 等待一小段时间确保状态更新
      await new Promise((resolve) => setTimeout(resolve, 100));

      // 使用新的API加载凭证数据
      await loadVouchersFromAPI();
    } catch {
      // 如果获取公司列表失败，回退到原有方法
      getList(0);
    }
  });
  const documentclick = () => {
    moreSeachShow.value = false;
    if (
      currentModificationMumberIndex !== -1 &&
      voucherList.value[currentModificationMumberIndex]
    ) {
      voucherList.value[currentModificationMumberIndex].inputIsShow = false;
      currentModificationMumberIndex = -1;
    }
  };
  onMounted(() => {
    document.addEventListener('click', documentclick);

    // 添加数据刷新事件监听器
    const handleReloadVoucherData = () => {
      console.log('收到刷新凭证数据事件');
      loadVouchersFromAPI();
    };

    window.addEventListener('reload-voucher-data', handleReloadVoucherData);

    // 组件卸载时移除事件监听器
    onUnmounted(() => {
      window.removeEventListener(
        'reload-voucher-data',
        handleReloadVoucherData,
      );
    });
  });
  onUnmounted(() => {
    document.removeEventListener('click', documentclick);
  });
  // 注释掉未使用的函数
  // const moreSeachShowFu = () => {
  //   moreSeachShow.value = !moreSeachShow.value;
  // };
  // const addVoucher = () => {};

  // 从AI凭证API加载数据，替换原有的getList方法
  async function loadVouchersFromAPI() {
    try {
      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      if (!companyName || !month) {
        message.warning('请先选择公司和月份');
        return;
      }

      useLoading.setShow(true);

      const response = await getCurrentVouchers({
        company_name: companyName,
        month,
      });

      // 检查响应数据结构
      const responseData = (response as any).data;

      // 根据实际的API响应结构获取items
      let items = [];
      if (responseData && responseData.data && responseData.data.items) {
        items = responseData.data.items;
      } else if (responseData && responseData.items) {
        items = responseData.items;
      } else {
        items = [];
      }

      // 转换AI凭证数据为查看凭证页面的格式
      const apiVouchers = items
        .map((item: any, index: number) => {
          // 确保voucher对象存在
          if (!item.voucher) {
            return null;
          }

          const voucher = item.voucher;
          const details = voucher.details || [];

          if (details.length === 0) {
            message.error('请至少添加一条有效的凭证明细');
            return null;
          }

          return {
            code: `${voucher.type || '记'}-${String(voucher.voucher_num || index + 1).padStart(3, '0')}`,
            confirmed: item.confirmed || false,
            credit: voucher.total_credit || 0,
            date: voucher.record_date || new Date().toISOString().split('T')[0],
            debit: voucher.total_debit || 0,
            detail: details.map((detail: any, detailIndex: number) => ({
              credit: detail.credit || 0,
              debit: detail.debit || 0,
              id: detail.id || detailIndex + 1,
              subjectName: detail.account || '',
              summary: detail.summary || '',
            })),
            executor: item.executor || 'system',
            id: voucher.unique_id || `voucher_${index}`,
            inputIsShow: false,
            isChecked: false,
            reviewed: item.confirmed || false,
            source_type: item.source_type || '未知',
            totalAmount: voucher.total_debit || voucher.total_credit || 0,
            type: voucher.type || '记',
          };
        })
        .filter(Boolean); // 过滤掉null值

      // 按创建时间或ID降序排列，最新的在前面
      const sortedVouchers = apiVouchers.sort((a: any, b: any) => {
        if (a.date && b.date) {
          return new Date(b.date).getTime() - new Date(a.date).getTime();
        }
        return String(b.id).localeCompare(String(a.id));
      });

      voucherList.value = sortedVouchers;
      allTotalLength.value = sortedVouchers.length;

      if (sortedVouchers.length === 0) {
        message.info('当前公司和月份没有凭证数据');
      } else {
        message.success(`成功加载 ${sortedVouchers.length} 条凭证数据`);
      }
    } catch {
      message.error('加载凭证数据失败，请重试');
      // 如果API失败，回退到原有的getList方法
      getList(0);
    } finally {
      useLoading.setShow(false);
    }
  }
  // const timeIntervalChange = (_e: any, data: any) => {
  //   searchesState.starTime = dayjs(`${data[0]}`, dateFormat);
  //   searchesState.endTime = dayjs(`${data[1]}`, dateFormat);
  //   getList(0);
  // };
  // 全选
  const selectAllChange = () => {
    if (selectAll.value) {
      // 全选操作
      voucherList.value.forEach((v) => {
        v.isChecked = true;
      });
    } else {
      voucherList.value.forEach((v) => {
        v.isChecked = false;
      });
    }
  };
  // 单选事件
  const singleChoice = () => {
    let isallselect = true; // 是否全选
    voucherList.value.forEach((v) => {
      if (!v.isChecked) {
        isallselect = false;
      }
    });
    selectAll.value = !!isallselect;
  };
  // 注释掉未使用的函数
  // const modifyVoucherNumber = (index: number) => {
  //   if (currentModificationMumberIndex === index) {
  //     voucherList.value[index].inputIsShow =
  //       !voucherList.value[index].inputIsShow;
  //     return;
  //   }
  //   changeVoucherNo.value = '';
  //   voucherList.value[index].inputIsShow = true;
  //   if (
  //     currentModificationMumberIndex !== -1 &&
  //     voucherList.value[currentModificationMumberIndex]
  //   ) {
  //     voucherList.value[currentModificationMumberIndex].inputIsShow = false;
  //   }
  //   currentModificationMumberIndex = index;
  // };
  // const saveVoucherNoClick = (index: number) => {
  //   // 凭证编号保存事件
  //   if (!changeVoucherNo.value) {
  //     message.warning('请输入编号');
  //     return;
  //   }
  //   const id = voucherList.value[index].id;
  //   changeVoucherNumber(id, changeVoucherNo.value).then((_res: any) => {
  //     // 处理响应
  //   });
  // };
  // const moreInquiries = () => {
  //   getList(0);
  // };
  // const resetmore = () => {
  //   searchesState.book_type_val = '不限';
  //   searchesState.voucherNumber = '';
  //   searchesState.checked1 = false;
  //   searchesState.checked2 = false;
  //   searchesState.accountNumber = '';
  //   searchesState.accountingType = 'All';
  //   searchesState.accountingInput = '';
  //   searchesState.moneyMin = '';
  //   searchesState.moneyMax = '';
  //   searchesState.abstract = '';
  //   searchesState.source = 'All';
  // };
  // 分页点击事件处理
  const pageClick = (page: number) => {
    getList(page - 1);
  };

  // AI功能相关方法
  // 应用生成的凭证
  function applyGeneratedVouchers() {
    message.success('凭证已应用到系统中');
    voucherStore.clearGeneratedVoucherData();
    voucherStore.clearBankReceiptData();
    hasGeneratedVoucher.value = false;
  }

  // 清除生成的凭证
  function clearGeneratedVouchers() {
    voucherStore.clearGeneratedVoucherData();
    voucherStore.clearBankReceiptData();
    hasGeneratedVoucher.value = false;
    message.info('已清除AI生成的凭证');
  }

  // 监听生成的凭证数据变化
  watch(
    () => voucherStore.generatedVoucherData,
    (newVal) => {
      if (newVal) {
        hasGeneratedVoucher.value = true;
        message.success('AI已生成新的凭证数据');
      }
    },
  );

  // 格式化数字
  function formatNumber(num: number): string {
    if (num === undefined || num === null) {
      return '0.00';
    }
    return num.toLocaleString('zh-CN', {
      maximumFractionDigits: 2,
      minimumFractionDigits: 2,
    });
  }

  // 公司选择框搜索过滤函数
  const filterCompanyOption = (input: string, option: any) => {
    if (!input) return true;

    const companyName = option?.children || option?.value || '';
    return String(companyName)
      .toLowerCase()
      .includes(input.toLowerCase());
  };

  // 监听公司和月份变化，重新加载数据
  watch([selectedCompany, selectedMonth], () => {
    loadVouchersFromAPI();
  });

  // 合并功能相关计算属性
  const selectedVouchers = computed(() => {
    return voucherList.value.filter((voucher) => voucher.isChecked);
  });

  const canMergeVouchers = computed(() => {
    const selected = selectedVouchers.value;
    // 至少选择2个凭证
    if (selected.length < 2) return false;
    // 只能合并银行回单类型的凭证
    return selected.every((voucher) => voucher.source_type === '银行回单');
  });

  // 带凭证ID的审核功能
  const handleVoucherReviewWithId = (voucherId: string) => {
    // 查找对应的凭证数据
    const voucherItem = voucherList.value.find((item) => item.id === voucherId);

    if (voucherItem) {
      // 将凭证数据存储到store中
      voucherStore.setReviewVoucherData({
        code: voucherItem.code,
        confirmed: voucherItem.confirmed,
        credit: voucherItem.credit || 0,
        date: voucherItem.date,
        debit: voucherItem.debit || 0,
        detail: voucherItem.detail,
        executor: voucherItem.executor,
        id: voucherItem.id,
        originalData: voucherItem, // 保存完整的原始数据
        source_type: voucherItem.source_type,
        totalAmount: voucherItem.totalAmount,
        type: voucherItem.type,
      });
    }

    // 使用内部路由跳转到凭证审核页面
    router.push('/bookkeeping/review');
  };

  // 批量审核功能
  const handleBatchAudit = async () => {
    // 获取选中的凭证
    const selectedVouchers = voucherList.value.filter((item) => item.isChecked);

    if (selectedVouchers.length === 0) {
      message.warning('请先选择要审核的凭证');
      return;
    }

    // 获取选中凭证的ID
    const voucherIds = selectedVouchers.map((item) => item.id);

    try {
      useLoading.setShow(true);

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      if (!selectedCompany.value) {
        message.error('请先选择公司');
        return;
      }

      const month = monthSelectionStore.getFormattedMonth();
      if (!month) {
        message.error('请先选择月份');
        return;
      }

      // 调用凭证写入API
      const result = await writeBackVouchers({
        company_name: selectedCompany.value,
        month,
        username,
        voucher_ids: voucherIds,
      });

      if (result.success) {
        message.success(`成功写入 ${selectedVouchers.length} 条凭证`);

        // 清除选中状态
        voucherList.value.forEach((item) => {
          item.isChecked = false;
        });
        selectAll.value = false;

        // 重新加载数据
        await loadVouchersFromAPI();
      } else {
        message.error(`凭证写入失败：${result.message}`);
      }
    } catch (error: any) {
      console.error('批量审核失败:', error);
      message.error(`批量审核失败：${error?.message || '未知错误'}`);
    } finally {
      useLoading.setShow(false);
    }
  };

  // 合并功能相关方法
  function handleMergeVouchers() {
    if (!canMergeVouchers.value) {
      message.warning('请选择至少2个银行回单类型的凭证进行合并');
      return;
    }
    mergeConfirmVisible.value = true;
  }

  async function confirmMergeVouchers() {
    try {
      mergeLoading.value = true;

      const companyName = selectedCompany.value;
      const month = monthSelectionStore.getFormattedMonth();

      const voucherUniqueIds = selectedVouchers.value.map((voucher) =>
        String(voucher.id),
      );

      console.log('合并凭证请求:', { companyName, month, voucherUniqueIds });

      const response = await mergeVouchers({
        company_name: companyName,
        month,
        voucher_unique_ids: voucherUniqueIds,
      });

      console.log('合并凭证响应:', response);

      const result = (response as any).data;

      if (result.result === '成功') {
        // 获取合并的凭证数量，兼容不同的数据结构
        let mergedCount = selectedVouchers.value.length;
        try {
          if (result.merged_info?.source_info?.bank_receipt_info) {
            const bankReceiptInfo =
              result.merged_info.source_info.bank_receipt_info;
            if (Array.isArray(bankReceiptInfo)) {
              mergedCount = bankReceiptInfo.length;
            } else if (bankReceiptInfo.merged_voucher_count) {
              mergedCount = bankReceiptInfo.merged_voucher_count;
            }
          }
        } catch (error) {
          console.warn('获取合并数量失败，使用默认值:', error);
        }

        message.success(`成功合并 ${mergedCount} 个凭证`);

        // 清除选择状态
        voucherList.value.forEach((item) => {
          item.isChecked = false;
        });
        selectAll.value = false;

        // 重新加载凭证数据
        await loadVouchersFromAPI();

        // 关闭确认弹窗
        mergeConfirmVisible.value = false;
      } else {
        message.error(result.err_msg || '合并凭证失败');
      }
    } catch (error) {
      console.error('合并凭证失败:', error);
      message.error('合并凭证失败，请重试');
    } finally {
      mergeLoading.value = false;
    }
  }

  function cancelMergeVouchers() {
    mergeConfirmVisible.value = false;
  }

  // 删除功能相关方法
  function handleDeleteVouchers() {
    const selected = selectedVouchers.value;
    if (selected.length === 0) {
      message.warning('请先选择要删除的凭证');
      return;
    }
    deleteConfirmVisible.value = true;
  }

  async function confirmDeleteVouchers() {
    try {
      deleteLoading.value = true;

      // 获取当前用户信息
      const username = userStore.userInfo?.username || '';

      if (!username) {
        message.error('无法获取用户信息，请重新登录');
        return;
      }

      // 获取选中凭证的ID
      const voucherIds = selectedVouchers.value.map((voucher) =>
        String(voucher.id),
      );

      console.log('删除凭证请求:', { ids: voucherIds, username });

      const response = await deleteVouchers({
        ids: voucherIds,
        username,
      });

      console.log('删除凭证响应:', response);

      if (response && response.data) {
        message.success(`成功删除 ${response.data.length} 条凭证`);
        await loadVouchersFromAPI();
      } else {
        message.error('删除凭证失败');
      }
    } catch (error) {
      console.error('删除凭证失败:', error);
      message.error('删除凭证失败，请重试');
    } finally {
      deleteLoading.value = false;
    }
  }

  function cancelDeleteVouchers() {
    deleteConfirmVisible.value = false;
  }

  watch([timeInterval, sortType], ([_new1, _newSort]) => {
    // 监听时间间隔和排序类型变化
  });
  watch(sortType, (_newSort) => {
    getList(0);
  });

  // 添加金额转大写的函数
  function convertCurrency(money: number): string {
    const cnNums = ['零', '壹', '贰', '叁', '肆', '伍', '陆', '柒', '捌', '玖'];
    const cnIntRadice = ['', '拾', '佰', '仟'];
    const cnDecUnits = ['角', '分'];
    const cnInteger = '整';
    const cnIntLast = '元';

    let integral = Math.floor(money);
    let decimal = Math.round((money - integral) * 100);
    let result = '';

    if (integral === 0) {
      result = '零元';
      if (decimal > 0) {
        result = '';
      }
    } else {
      let zeroCount = 0;
      const IntLen = integral.toString().length;
      for (let i = 0; i < IntLen; i++) {
        const n = integral % 10;
        if (n < 0 || n >= cnNums.length || i >= cnIntRadice.length) {
          integral = Math.floor(integral / 10);
          continue;
        }
        const p1 = cnNums[n] || '';
        const p2 = cnIntRadice[i] || '';
        if (n === 0) {
          zeroCount++;
        } else {
          if (zeroCount > 0) {
            result = (cnNums[0] || '') + result;
          }
          zeroCount = 0;
          result = p1 + p2 + result;
        }
        integral = Math.floor(integral / 10);
      }
      result = result + cnIntLast;
    }

    if (decimal > 0) {
      for (let i = 0; i < 2; i++) {
        const n = decimal % 10;
        if (n !== 0 && n < cnNums.length && i < cnDecUnits.length) {
          const num = cnNums[n] || '';
          const unit = cnDecUnits[i] || '';
          result = result + num + unit;
        }
        decimal = Math.floor(decimal / 10);
      }
    } else {
      result = result + cnInteger;
    }

    return result;
  }
</script>
<template>
  <OuterBoundary>
    <div class="cont flex flex-1 flex-col rounded-md bg-white">
      <!-- AI生成凭证提示 -->
      <div v-if="hasGeneratedVoucher" class="ai-generated-notice p-2">
        <a-alert
          type="info"
          show-icon
          message="AI已生成凭证数据"
          description="从银行回单生成的凭证数据已添加到凭证列表中。"
        >
          <template #action>
            <div class="ai-generated-actions">
              <a-button
                type="primary"
                size="small"
                @click="applyGeneratedVouchers"
              >
                应用到系统
              </a-button>
              <a-button size="small" @click="clearGeneratedVouchers">
                清除
              </a-button>
            </div>
          </template>
        </a-alert>
      </div>

      <div class="flex flex-1 flex-col">
        <div class="p-2">
          <a-flex justify="space-between">
            <a-space>
              <!-- 公司选择器 -->
              <a-select
                v-model:value="selectedCompany"
                placeholder="选择公司"
                style="width: 260px"
                size="small"
                show-search
                :filter-option="filterCompanyOption"
              >
                <a-select-option
                  v-for="company in companyList"
                  :key="company.id"
                  :value="company.name"
                >
                  {{ company.name }}
                </a-select-option>
              </a-select>

              <!-- 月份选择器 -->
              <a-date-picker
                :value="monthSelectionStore.selectedMonth"
                @change="
                  (date) =>
                    monthSelectionStore.setSelectedMonth(
                      (date as any) || dayjs(),
                    )
                "
                picker="month"
                size="small"
                placeholder="选择月份"
                style="width: 120px"
              />

              <!-- <a-range-picker
                v-model:value="timeInterval"
                picker="month"
                size="small"
                @change="timeIntervalChange"
              />
              <a-input-search
                v-model:value="seachInput"
                placeholder="凭证号"
                style="width: 100px"
                @search="onSearchInput"
                size="small"
              /> -->
              <!-- <div class="move-seach">
                <a-button
                  type="primary"
                  size="small"
                  @click.stop="moreSeachShowFu"
                >
                  更多查询
                </a-button>
                <div v-if="moreSeachShow" class="hideinfo" @click.stop>
                  <ul class="">
                    <li class="li1">会计期间</li>
                    <li class="ii2">
                      <a-date-picker
                        size="small"
                        picker="month"
                        class="stylew1"
                        v-model:value="searchesState.starTime"
                      />
                      至
                      <a-date-picker
                        size="small"
                        picker="month"
                        class="stylew1"
                        v-model:value="searchesState.endTime"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">凭证字</li>
                    <li class="ii2">
                      <a-select
                        size="small"
                        :options="
                          [{ value: '不限' }].concat(commondata.bookTypes)
                        "
                        v-model:value="searchesState.book_type_val"
                        :field-names="{ label: 'value', value: 'value' }"
                        class="stylew2"
                      />
                      至
                      <a-input
                        v-model:value="searchesState.voucherNumber"
                        placeholder="凭证号"
                        class="stylew3"
                        size="small"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">科目编号</li>
                    <li class="ii2">
                      <a-select
                        :field-names="{
                          label: 'text',
                          value: 'code',
                        }"
                        show-search
                        :options="useSubject.selectdata"
                        class="stylew4"
                        size="small"
                        v-model:value="searchesState.accountNumber"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">辅助核算</li>
                    <li class="ii2">
                      <a-select
                        size="small"
                        style="width: 100px"
                        :options="commondata.auxiliaryAccounting"
                        class="stylew2"
                        v-model:value="searchesState.accountingType"
                      />
                      至
                      <a-input
                        v-model:value="searchesState.accountingInput"
                        placeholder="请输入编码名称"
                        class="stylew3"
                        size="small"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">金额</li>
                    <li class="ii2">
                      <a-input
                        v-model:value="searchesState.moneyMin"
                        placeholder="最低金额"
                        class="stylew1"
                        size="small"
                      />
                      至
                      <a-input
                        v-model:value="searchesState.moneyMax"
                        placeholder="最高金额"
                        class="stylew1"
                        size="small"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">摘要</li>
                    <li class="ii2">
                      <a-input
                        v-model:value="searchesState.abstract"
                        placeholder="请输入凭证摘要"
                        class="stylew4"
                        size="small"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="li1">来源</li>
                    <li class="ii2">
                      <a-select
                        size="small"
                        style="width: 100px"
                        :options="commondata.voucherType"
                        class="stylew4"
                        v-model:value="searchesState.source"
                      />
                    </li>
                  </ul>
                  <ul>
                    <li class="ii2">
                      <a-checkbox v-model:checked="searchesState.checked1">
                        只显示设置了有现金流量项目科目的凭证
                      </a-checkbox>
                    </li>
                  </ul>
                  <ul>
                    <li class="ii2">
                      <a-checkbox v-model:checked="searchesState.checked2">
                        只显示有标注的凭证
                      </a-checkbox>
                    </li>
                  </ul>
                  <a-divider style="margin: 10px 0" />
                  <ul>
                    <li class="li2"></li>
                    <li class="li3">
                      <a-button size="small" @click="resetmore">重置</a-button>
                      &nbsp;
                      <a-button
                        size="small"
                        type="primary"
                        @click="moreInquiries"
                      >
                        确定
                      </a-button>
                    </li>
                  </ul>
                </div>
              </div> -->
              <!-- <a-button type="primary" size="small" @click="addVoucher">
                新增凭证
              </a-button> -->
            </a-space>
            <a-space>
              <a-button size="small" @click="loadVouchersFromAPI" v-if="isDev">
                重新加载数据
              </a-button>
              <a-button
                size="small"
                type="primary"
                @click="handleBatchAudit"
                :disabled="!voucherList.some((item) => item.isChecked)"
              >
                批量审核
              </a-button>
              <a-button
                size="small"
                type="default"
                @click="handleMergeVouchers"
                :disabled="!canMergeVouchers"
                :loading="mergeLoading"
              >
                合并凭证 ({{ selectedVouchers.length }})
              </a-button>
              <a-button
                size="small"
                danger
                @click="handleDeleteVouchers"
                :disabled="!voucherList.some((item) => item.isChecked)"
                :loading="deleteLoading"
              >
                删除凭证 ({{ selectedVouchers.length }})
              </a-button>
            </a-space>
          </a-flex>
        </div>
        <div class="listcont m-2 flex-1" flex="1">
          <div class="tablelist">
            <table>
              <thead class="sticky-row">
                <tr>
                  <th style="width: 60px; text-align: center">
                    <a-checkbox
                      v-model:checked="selectAll"
                      @change="selectAllChange"
                    />
                  </th>
                  <th style="width: 35%; text-align: left">摘要</th>
                  <th style="width: 25%; text-align: left">科目</th>
                  <th style="width: 20%; text-align: right">借方金额</th>
                  <th style="width: 20%; text-align: right">贷方金额</th>
                </tr>
              </thead>
              <tbody>
                <template v-for="item in voucherList" :key="item.id">
                  <!-- 凭证头部行 -->
                  <tr>
                    <td :rowspan="item.detail.length + 2" class="checkbox-cell">
                      <div class="h-[100%] pt-2 text-center">
                        <a-checkbox
                          @change="singleChoice"
                          v-model:checked="item.isChecked"
                        />
                      </div>
                    </td>
                    <td colspan="4" class="voucher-header-cell">
                      <div class="btns d1">
                        <div class="voucher-info">
                          <span class="info-item">
                            凭证字号：{{ item.code }}
                          </span>
                          <span class="info-item">日期：{{ item.date }}</span>
                          <span class="info-item">
                            补全状态：{{ item.confirmed ? '已补全' : '未补全' }}
                          </span>
                          <span class="info-item">
                            写入状态：{{ item.written ? '已写入' : '未写入' }}
                          </span>
                          <span class="info-item">
                            原始凭证类型：{{ item.source_type }}
                          </span>
                        </div>
                        <div>
                          <span
                            class="review-btn"
                            @click="handleVoucherReviewWithId(item.id)"
                          >
                            审核
                          </span>
                        </div>
                      </div>
                    </td>
                  </tr>

                  <!-- 凭证明细行 -->
                  <tr v-for="itm in item.detail" :key="itm.id">
                    <td style="text-align: left">
                      <div class="d1">{{ itm.summary }}</div>
                    </td>
                    <td style="text-align: left">
                      <div class="d1">{{ itm.subjectName }}</div>
                    </td>
                    <td style="text-align: right">
                      <div class="d1">
                        <span v-if="itm.debit > 0">
                          ¥{{ formatNumber(itm.debit) }}
                        </span>
                      </div>
                    </td>
                    <td style="text-align: right">
                      <div class="d1">
                        <span v-if="itm.credit > 0">
                          ¥{{ formatNumber(itm.credit) }}
                        </span>
                      </div>
                    </td>
                  </tr>

                  <!-- 凭证合计行 -->
                  <tr>
                    <td style="font-weight: bold; text-align: left">
                      <div class="d1">
                        合计：{{ convertCurrency(item.debit || 0) }}
                      </div>
                    </td>
                    <td style="text-align: left">
                      <div class="d1"></div>
                    </td>
                    <td style="font-weight: bold; text-align: right">
                      <div class="d1">¥{{ formatNumber(item.debit || 0) }}</div>
                    </td>
                    <td style="font-weight: bold; text-align: right">
                      <div class="d1">
                        ¥{{ formatNumber(item.credit || 0) }}
                      </div>
                    </td>
                  </tr>
                </template>
              </tbody>
            </table>
            <div
              v-if="voucherList.length === 0"
              class="empyt mt-10 text-center"
            >
              <div>暂无数据</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 合并凭证确认弹窗 -->
    <a-modal
      v-model:open="mergeConfirmVisible"
      title="合并凭证确认"
      :confirm-loading="mergeLoading"
      @ok="confirmMergeVouchers"
      @cancel="cancelMergeVouchers"
    >
      <div class="merge-confirm-content">
        <p>您确定要合并以下 {{ selectedVouchers.length }} 个银行回单凭证吗？</p>
        <div class="selected-vouchers-container">
          <!-- 表头 -->
          <div class="vouchers-header">
            <span class="header-info">凭证信息</span>
            <span class="header-date">日期</span>
            <span class="header-amount">金额</span>
          </div>
          <!-- 凭证列表 -->
          <div class="selected-vouchers-list">
            <div
              v-for="voucher in selectedVouchers"
              :key="voucher.id"
              class="selected-voucher-item"
            >
              <span class="voucher-info">
                {{ voucher.code }}
              </span>
              <span class="voucher-date">{{ voucher.date }}</span>
              <span class="voucher-amount">
                借方: ¥{{ formatNumber(voucher.debit || 0) }}
                <br />
                贷方: ¥{{ formatNumber(voucher.credit || 0) }}
              </span>
            </div>
          </div>
        </div>
        <p class="merge-warning">
          <strong>注意：</strong>
          合并后的凭证将替换原有的多个凭证，此操作不可撤销。
        </p>
      </div>
    </a-modal>

    <!-- 删除凭证确认弹窗 -->
    <a-modal
      v-model:open="deleteConfirmVisible"
      title="删除凭证确认"
      :confirm-loading="deleteLoading"
      @ok="confirmDeleteVouchers"
      @cancel="cancelDeleteVouchers"
    >
      <div class="delete-confirm-content">
        <p>您确定要删除以下 {{ selectedVouchers.length }} 个凭证吗？</p>
        <div class="selected-vouchers-container">
          <!-- 表头 -->
          <div class="vouchers-header">
            <span class="header-info">凭证信息</span>
            <span class="header-date">日期</span>
            <span class="header-amount">金额</span>
          </div>
          <!-- 凭证列表 -->
          <div class="selected-vouchers-list">
            <div
              v-for="voucher in selectedVouchers"
              :key="voucher.id"
              class="selected-voucher-item"
            >
              <span class="voucher-info">
                {{ voucher.code }}
              </span>
              <span class="voucher-date">{{ voucher.date }}</span>
              <span class="voucher-amount">
                借方: ¥{{ formatNumber(voucher.debit || 0) }}
                <br />
                贷方: ¥{{ formatNumber(voucher.credit || 0) }}
              </span>
            </div>
          </div>
        </div>
        <p class="delete-warning">
          <strong>警告：</strong>
          删除后的凭证将无法恢复，此操作不可撤销。请谨慎操作！
        </p>
      </div>
    </a-modal>
  </OuterBoundary>
</template>
<style lang="scss" scoped>
  .sticky-row {
    position: sticky;
    top: 0; //固定行
    z-index: 1;
    background-color: rgb(250 250 250);

    tr {
      border: solid 1px rgb(233 233 233);
    }

    th {
      outline: rgb(233 233 233) solid 0.5px;
    }
  }

  .move-seach {
    position: relative;
    display: inline-block;

    .hideinfo {
      position: absolute;
      top: 30px;
      left: 0;
      z-index: 2;
      width: 465px;
      padding: 10px;
      background-color: #fff;
      border: solid 1px #eee;
      border-radius: 10px;

      .stylew1 {
        width: 170px;
      }

      .stylew2 {
        width: 100px;
      }

      .stylew3 {
        width: 240px;
      }

      .stylew4 {
        width: 362px;
      }

      ul {
        display: flex;
        margin: 5px 0;

        .li1 {
          width: 80px;
        }

        .li2 {
          flex: 1;
        }
      }
    }
  }

  .listcont {
    position: relative;
    flex: 1;
    min-height: 400px;

    .tablelist {
      width: 100%;
      height: 100%;
      overflow: auto;

      table {
        width: 100%;
        font-size: 12px;
        table-layout: fixed;
        border-collapse: collapse;

        thead {
          font-size: 14px;
          background-color: rgb(250 250 250);
        }

        th,
        td {
          height: 40px;
          padding: 8px;
          vertical-align: middle;
          border: solid 1px rgb(233 233 233);
        }

        th {
          font-weight: 600;
        }

        .d1 {
          width: 100%;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .checkbox-cell {
          background-color: white;
        }

        .voucher-header-cell {
          background-color: #f0f7ff;

          &:hover {
            background-color: #e6f4ff;
          }
        }

        .btns {
          display: flex;
          flex-direction: row;
          align-items: center;
          justify-content: space-between;

          .voucher-info {
            display: flex;
            gap: 16px;
            align-items: center;

            .info-item {
              white-space: nowrap;
            }
          }

          .review-btn {
            display: inline-flex;
            align-items: center;
            padding: 6px 16px;
            font-size: 13px;
            font-weight: 500;
            color: #1677ff;
            cursor: pointer;
            background-color: #e6f4ff;
            border: 1px solid #91caff;
            border-radius: 6px;
            transition: all 0.2s ease;

            &:hover {
              color: #0958d9;
              background-color: #bae0ff;
              border-color: #69b1ff;
            }

            &:active {
              color: #0958d9;
              background-color: #91caff;
              border-color: #0958d9;
            }
          }
        }
      }
    }
  }

  /* AI功能相关样式 */
  .ai-generated-notice {
    .ai-generated-actions {
      display: flex;
      gap: 8px;
    }
  }

  .ai-btn {
    color: white;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;

    &:hover {
      color: white;
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }
  }

  /* 合并凭证弹窗样式 */
  .merge-confirm-content {
    .selected-vouchers-container {
      margin: 16px 0;
      overflow: hidden;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }

    .vouchers-header {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      gap: 12px;
      align-items: center;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e8e8e8;

      .header-info {
        text-align: left;
      }

      .header-date {
        text-align: center;
      }

      .header-amount {
        text-align: right;
      }
    }

    .selected-vouchers-list {
      max-height: 300px;
      padding: 0;
      overflow-y: auto;
      background-color: #fafafa;
    }

    .selected-voucher-item {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      gap: 12px;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f0f0f0;
      }

      &:last-child {
        border-bottom: none;
      }

      .voucher-info {
        font-size: 14px;
        font-weight: 500;
        color: #1890ff;
        text-align: left;
      }

      .voucher-date {
        font-size: 13px;
        color: #666;
        text-align: center;
      }

      .voucher-amount {
        font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        text-align: right;

        br {
          margin: 2px 0;
        }
      }
    }

    .merge-warning {
      padding: 12px;
      margin-top: 16px;
      font-size: 14px;
      color: #d46b08;
      background-color: #fff7e6;
      border: 1px solid #ffd591;
      border-radius: 6px;
    }
  }

  /* 删除凭证弹窗样式 */
  .delete-confirm-content {
    .selected-vouchers-container {
      margin: 16px 0;
      overflow: hidden;
      border: 1px solid #f0f0f0;
      border-radius: 6px;
    }

    .vouchers-header {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      gap: 12px;
      align-items: center;
      padding: 12px 16px;
      font-size: 14px;
      font-weight: 600;
      color: #333;
      background-color: #f5f5f5;
      border-bottom: 1px solid #e8e8e8;

      .header-info {
        text-align: left;
      }

      .header-date {
        text-align: center;
      }

      .header-amount {
        text-align: right;
      }
    }

    .selected-vouchers-list {
      max-height: 300px;
      padding: 0;
      overflow-y: auto;
      background-color: #fafafa;
    }

    .selected-voucher-item {
      display: grid;
      grid-template-columns: 1fr 1fr 2fr;
      gap: 12px;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #e8e8e8;
      transition: background-color 0.2s;

      &:hover {
        background-color: #f0f0f0;
      }

      &:last-child {
        border-bottom: none;
      }

      .voucher-info {
        font-size: 14px;
        font-weight: 500;
        color: #ff4d4f;
        text-align: left;
      }

      .voucher-date {
        font-size: 13px;
        color: #666;
        text-align: center;
      }

      .voucher-amount {
        font-family: Monaco, Menlo, 'Ubuntu Mono', monospace;
        font-size: 12px;
        line-height: 1.4;
        color: #333;
        text-align: right;

        br {
          margin: 2px 0;
        }
      }
    }

    .delete-warning {
      padding: 12px;
      margin-top: 16px;
      font-size: 14px;
      color: #ff4d4f;
      background-color: #fff2f0;
      border: 1px solid #ffccc7;
      border-radius: 6px;
    }
  }
</style>
